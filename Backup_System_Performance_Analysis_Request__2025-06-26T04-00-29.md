[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Create Simple Backup System DESCRIPTION:Create a simplified backup system with clear logging, reasonable timeouts, and simple retry logic
-[x] NAME:Add API Connection Testing DESCRIPTION:Add simple tests to verify Devo API and OSS API connections work before starting backup
-[x] NAME:Implement Clear Progress Logging DESCRIPTION:Add clear, simple logging that shows exactly what the system is doing at each step
-[x] NAME:Add Simple Retry Mechanism DESCRIPTION:Implement simple retry logic with reasonable delays and clear error messages
-[x] NAME:Add Chunking for Large Tables DESCRIPTION:Add simple chunking process for large tables to prevent timeouts
-[x] NAME:Clean Up Temp Files DESCRIPTION:Ensure temporary files are properly cleaned up after each table
-[x] NAME:Audit Current Codebase DESCRIPTION:Identify all files, functions, parameters, and variables that are not being used in the backup system
-[x] NAME:Remove Unused Files DESCRIPTION:Delete any configuration files, scripts, or other files that are not needed for the backup system
-[x] NAME:Clean Up Code DESCRIPTION:Remove unused functions, parameters, variables, and imports from the main backup file
-[x] NAME:Verify System Still Works DESCRIPTION:Test the cleaned backup system to ensure it still works correctly after cleanup